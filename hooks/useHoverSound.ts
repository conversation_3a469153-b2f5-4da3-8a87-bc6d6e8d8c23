'use client';

import { useEffect, useRef } from 'react';
import { useSoundContext } from '@/context/SoundContext';

export const useHoverSound = () => {
    const audioRef = useRef<HTMLAudioElement | null>(null);
    const { isSoundEnabled } = useSoundContext();

    useEffect(() => {
        // Web Audio API ile programatik ses oluşturma
        const createBeepSound = () => {
            try {
                const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

                const playBeep = () => {
                    if (!isSoundEnabled) return;

                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);

                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime); // 800Hz beep
                    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                    gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);
                    gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.1);

                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.1);
                };

                return playBeep;
            } catch (error) {
                console.warn('Web Audio API not supported, falling back to HTML5 audio');
                return null;
            }
        };

        const beepFunction = createBeepSound();

        if (!beepFunction) {
            // Fallback to HTML5 audio
            try {
                audioRef.current = new Audio('/sounds/trinity.wav');
                audioRef.current.volume = 0.3;
                audioRef.current.preload = 'auto';

                audioRef.current.addEventListener('canplaythrough', () => {
                    console.log('Trinity sound loaded successfully');
                });

                audioRef.current.addEventListener('error', (e) => {
                    console.error('Trinity sound failed to load:', e);
                });
            } catch (error) {
                console.error('Failed to create audio element:', error);
            }
        } else {
            // Store the beep function for use
            (audioRef as any).beepFunction = beepFunction;
        }

        return () => {
            if (audioRef.current) {
                audioRef.current.pause();
                audioRef.current = null;
            }
        };
    }, []);

    const playSound = () => {
        if (!isSoundEnabled) return;

        // Try Web Audio API beep first
        if ((audioRef as any).beepFunction) {
            try {
                (audioRef as any).beepFunction();
                return;
            } catch (error) {
                console.warn('Beep function failed:', error);
            }
        }

        // Fallback to HTML5 audio
        if (audioRef.current && audioRef.current instanceof HTMLAudioElement) {
            audioRef.current.currentTime = 0;
            audioRef.current.play().catch((error) => {
                console.warn('Audio play failed:', error);
            });
        }
    };

    const stopSound = () => {
        // Web Audio API beeps stop automatically, no need to stop them
        // Only stop HTML5 audio if it exists
        if (audioRef.current && audioRef.current instanceof HTMLAudioElement) {
            audioRef.current.pause();
            audioRef.current.currentTime = 0;
        }
    };

    return { playSound, stopSound };
};