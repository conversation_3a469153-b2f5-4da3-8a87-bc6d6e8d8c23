'use client';

import { useEffect, useRef } from 'react';
import { useSoundContext } from '@/context/SoundContext';

export const useHoverSound = () => {
    const audioRef = useRef<HTMLAudioElement | null>(null);
    const { isSoundEnabled } = useSoundContext();

    useEffect(() => {
        console.log('useHoverSound: Initializing audio...');
        audioRef.current = new Audio('/sounds/trinity.wav');
        audioRef.current.volume = 0.8; // Ses seviyesini artırdım
        audioRef.current.preload = 'auto';

        // User interaction için audio context'i unlock etme
        const unlockAudio = () => {
            if (audioRef.current) {
                audioRef.current.play().then(() => {
                    audioRef.current!.pause();
                    audioRef.current!.currentTime = 0;
                    console.log('Audio context unlocked');
                }).catch(() => {
                    console.log('Audio unlock failed');
                });
            }
        };

        // İlk tıklamada audio context'i unlock et
        document.addEventListener('click', unlockAudio, { once: true });
        document.addEventListener('touchstart', unlockAudio, { once: true });

        audioRef.current.addEventListener('loadstart', () => {
            console.log('Audio: Load started');
        });

        audioRef.current.addEventListener('canplay', () => {
            console.log('Audio: Can play');
        });

        audioRef.current.addEventListener('error', (e) => {
            console.error('Audio: Error loading', e);
        });

        return () => {
            document.removeEventListener('click', unlockAudio);
            document.removeEventListener('touchstart', unlockAudio);
            if (audioRef.current) {
                audioRef.current.pause();
                audioRef.current = null;
            }
        };
    }, []);

    const playSound = () => {
        console.log('playSound called, isSoundEnabled:', isSoundEnabled);
        if (audioRef.current && isSoundEnabled) {
            console.log('Playing sound...');
            audioRef.current.currentTime = 0;

            // Promise ile ses çalma
            const playPromise = audioRef.current.play();

            if (playPromise !== undefined) {
                playPromise
                    .then(() => {
                        console.log('Audio played successfully');
                    })
                    .catch((error) => {
                        console.error('Audio play failed:', error);
                        // Eğer autoplay engellendiyse, kullanıcıya bilgi ver
                        if (error.name === 'NotAllowedError') {
                            console.log('Autoplay blocked - user interaction required');
                        }
                    });
            }
        } else {
            console.log('Sound not played - audioRef:', !!audioRef.current, 'soundEnabled:', isSoundEnabled);
        }
    };

    const stopSound = () => {
        if (audioRef.current) {
            audioRef.current.pause();
            audioRef.current.currentTime = 0;
        }
    };

    return { playSound, stopSound };
};