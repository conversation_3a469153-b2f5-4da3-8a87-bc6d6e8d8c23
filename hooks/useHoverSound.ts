'use client';

import { useEffect, useRef } from 'react';
import { useSoundContext } from '@/context/SoundContext';

export const useHoverSound = () => {
    const audioRef = useRef<HTMLAudioElement | null>(null);
    const { isSoundEnabled } = useSoundContext();

    useEffect(() => {
        audioRef.current = new Audio('/sounds/trinity.mp4');
        audioRef.current.volume = 0.5;

        return () => {
            if (audioRef.current) {
                audioRef.current.pause();
                audioRef.current = null;
            }
        };
    }, []);

    const playSound = () => {
        if (audioRef.current && isSoundEnabled) {
            audioRef.current.currentTime = 0;
            audioRef.current.play().catch(() => {});
        }
    };

    const stopSound = () => {
        if (audioRef.current) {
            audioRef.current.pause();
            audioRef.current.currentTime = 0;
        }
    };

    return { playSound, stopSound };
};