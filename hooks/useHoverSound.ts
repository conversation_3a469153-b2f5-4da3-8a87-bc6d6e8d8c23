'use client';

import { useEffect, useRef } from 'react';
import { useSoundContext } from '@/context/SoundContext';

export const useHoverSound = () => {
    const audioRef = useRef<HTMLAudioElement | null>(null);
    const { isSoundEnabled } = useSoundContext();

    useEffect(() => {
        audioRef.current = new Audio('/sounds/trinity.wav');
        audioRef.current.volume = 1.0; // Maksimum ses seviyesi
        audioRef.current.preload = 'auto';

        return () => {
            if (audioRef.current) {
                audioRef.current.pause();
                audioRef.current = null;
            }
        };
    }, []);

    const playSound = () => {
        if (audioRef.current && isSoundEnabled) {
            audioRef.current.currentTime = 0;
            audioRef.current.play().catch(() => {
                // Ses çalmazsa sessizce devam et
            });
        }
    };

    const stopSound = () => {
        if (audioRef.current) {
            audioRef.current.pause();
            audioRef.current.currentTime = 0;
        }
    };

    return { playSound, stopSound };
};