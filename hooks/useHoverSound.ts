'use client';

import { useEffect, useRef } from 'react';
import { useSoundContext } from '@/context/SoundContext';

export const useHoverSound = () => {
    const audioRef = useRef<HTMLAudioElement | null>(null);
    const { isSoundEnabled } = useSoundContext();

    useEffect(() => {
        console.log('useHoverSound: Initializing audio...');
        audioRef.current = new Audio('/sounds/trinity.wav');
        audioRef.current.volume = 0.5;

        audioRef.current.addEventListener('loadstart', () => {
            console.log('Audio: Load started');
        });

        audioRef.current.addEventListener('canplay', () => {
            console.log('Audio: Can play');
        });

        audioRef.current.addEventListener('error', (e) => {
            console.error('Audio: Error loading', e);
        });

        return () => {
            if (audioRef.current) {
                audioRef.current.pause();
                audioRef.current = null;
            }
        };
    }, []);

    const playSound = () => {
        console.log('playSound called, isSoundEnabled:', isSoundEnabled);
        if (audioRef.current && isSoundEnabled) {
            console.log('Playing sound...');
            audioRef.current.currentTime = 0;
            audioRef.current.play().catch((error) => {
                console.error('Audio play failed:', error);
            });
        } else {
            console.log('Sound not played - audioRef:', !!audioRef.current, 'soundEnabled:', isSoundEnabled);
        }
    };

    const stopSound = () => {
        if (audioRef.current) {
            audioRef.current.pause();
            audioRef.current.currentTime = 0;
        }
    };

    return { playSound, stopSound };
};