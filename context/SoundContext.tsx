'use client';

import React, { createContext, useContext, useState } from 'react';
import { Volume2, VolumeX } from 'lucide-react';

interface SoundContextType {
    isSoundEnabled: boolean;
    toggleSound: () => void;
}

const SoundContext = createContext<SoundContextType>({
    isSoundEnabled: true,
    toggleSound: () => {}
});

export const useSoundContext = () => useContext(SoundContext);

export const SoundProvider = ({ children }: { children: React.ReactNode }) => {
    const [isSoundEnabled, setIsSoundEnabled] = useState(true);

    console.log('SoundProvider: isSoundEnabled =', isSoundEnabled);

    const toggleSound = () => {
        setIsSoundEnabled(prev => {
            console.log('SoundProvider: Toggling sound from', prev, 'to', !prev);
            return !prev;
        });
    };

    return (
        <SoundContext.Provider value={{ isSoundEnabled, toggleSound }}>
            {children}
            <button
                onClick={toggleSound}
                className="fixed bottom-4 right-4 z-50 p-2 rounded-full bg-white/5
                    hover:bg-white/10 transition-colors backdrop-blur-sm"
                title={`Sound ${isSoundEnabled ? 'On' : 'Off'}`}
            >
                {isSoundEnabled ? (
                    <Volume2 className="w-5 h-5 text-white/60" />
                ) : (
                    <VolumeX className="w-5 h-5 text-white/60" />
                )}
            </button>
        </SoundContext.Provider>
    );
};