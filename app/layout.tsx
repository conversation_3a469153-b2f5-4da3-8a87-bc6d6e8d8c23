// app/layout.tsx
import type { Metadata } from "next";
import { AuthProvider } from "@/context/AuthContext";
import { SoundProvider } from "@/context/SoundContext";
import { MatrixLoaderWrapper } from "@/components/matrix/MatrixLoaderWrapper";
import "./globals.css";

export const metadata: Metadata = {
    title: '<PERSON><PERSON><PERSON> - Digital Entrepreneur',
    description: 'Digital entrepreneur and venture architect - Exploring the matrix of innovation through enterprise development',
    keywords: ['Digital Entrepreneur', 'Venture Development', 'Enterprise Architecture', 'Innovation Matrix', 'Tech Ventures'],
    icons: {
        icon: [
            { url: '/favicon.ico' },
            { url: '/favicon.svg', type: 'image/svg+xml' },
        ],
    },
};

export default function RootLayout({
                                       children,
                                   }: {
    children: React.ReactNode;
}) {
    return (
        <html lang="en" className="scroll-smooth">
        <body>
        <AuthProvider>
            <SoundProvider>
                <MatrixLoaderWrapper>
                    {children}
                </MatrixLoaderWrapper>
            </SoundProvider>
        </AuthProvider>
        </body>
        </html>
    );
}