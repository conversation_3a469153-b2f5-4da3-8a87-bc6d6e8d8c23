'use client';

import React, {useState, useEffect} from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {motion} from 'framer-motion';
import {ArrowLeft, Plus, Edit, Trash, Calendar, X} from 'lucide-react';
import BaseLayout from '@/components/BaseLayout';
import Logo from "@/components/Logo";
import {MatrixText} from '@/components/matrix/MatrixText';
import {TerminalWindow} from '@/components/matrix/TerminalWindow';
import {useAuth} from '@/context/AuthContext';
import {useMemories} from '@/hooks/useMemories';
import {Memory} from '@/types/memory';
import MemoryModal from '@/components/MemoryModal';
import {AdaptiveImage} from '@/components/AdaptiveImage';
import {CSSMasonryGrid} from '@/components/MasonryGrid';

// Image Modal Component
const ImageModal: React.FC<{ imageUrl: string; onClose: () => void }> = ({imageUrl, onClose}) => {
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });

    const handleImageLoad = (event: React.SyntheticEvent<HTMLImageElement>) => {
        const img = event.currentTarget;
        setImageDimensions({ width: img.naturalWidth, height: img.naturalHeight });
        setImageLoaded(true);
    };

    // Calculate optimal modal size based on image aspect ratio
    const isPortrait = imageDimensions.height > imageDimensions.width;
    const aspectRatio = imageDimensions.width / imageDimensions.height;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/90 backdrop-blur-sm"
             onClick={onClose}>
            <div className="relative w-full h-full flex items-center justify-center p-4">
                <button
                    onClick={onClose}
                    className="absolute top-4 right-4 z-10 p-2 rounded-full bg-black/50 hover:bg-black/70 transition-colors border border-white/20"
                >
                    <X className="w-6 h-6 text-white"/>
                </button>

                <div className="relative flex items-center justify-center w-full h-full">
                    <div className={`relative ${
                        isPortrait && imageLoaded
                            ? 'h-[90vh] w-auto max-w-[70vw]'
                            : 'w-[90vw] h-auto max-h-[90vh]'
                    }`}>
                        <Image
                            src={imageUrl}
                            alt="Memory"
                            width={0}
                            height={0}
                            className="object-contain"
                            onLoad={handleImageLoad}
                            style={{
                                width: '100%',
                                height: 'auto',
                                maxWidth: '90vw',
                                maxHeight: '90vh'
                            }}
                            sizes="90vw"
                            priority
                        />

                        {!imageLoaded && (
                            <div className="absolute inset-0 flex items-center justify-center bg-white/5 rounded-lg min-h-[200px] min-w-[200px]">
                                <div className="text-white/60">Loading full size image...</div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

const MemoryCard: React.FC<{
    memory: Memory;
    onEdit?: () => void;
    onDelete?: () => void;
    isAdmin: boolean;
    onImageClick: (url: string) => void;
}> = ({
          memory,
          onEdit,
          onDelete,
          isAdmin,
          onImageClick
      }) => {
    return (
        <motion.div
            initial={{opacity: 0, y: 20}}
            animate={{opacity: 1, y: 0}}
            className="relative group w-full"
            style={{ breakInside: 'avoid' }}
        >
            <TerminalWindow
                title={`Memory_${memory.date}.dmg`}
                className="backdrop-blur-lg bg-white/10 rounded-3xl shadow-xl
                    hover:bg-white/[0.15] transition-all duration-300 w-full"
            >
                {isAdmin && (
                    <div className="absolute top-2 right-2 z-10 flex gap-2">
                        <button
                            onClick={onEdit}
                            className="p-2 rounded-full bg-white/10 hover:bg-white/20
                                transition-colors opacity-0 group-hover:opacity-100"
                        >
                            <Edit className="w-4 h-4 text-white/80"/>
                        </button>
                        <button
                            onClick={onDelete}
                            className="p-2 rounded-full bg-red-500/20 hover:bg-red-500/30
                                transition-colors opacity-0 group-hover:opacity-100"
                        >
                            <Trash className="w-4 h-4 text-red-400"/>
                        </button>
                    </div>
                )}

                <div className="p-6">
                    {memory.mediaType === 'image' ? (
                        <AdaptiveImage
                            src={memory.mediaUrl}
                            alt={memory.title}
                            className="mb-4"
                            onClick={() => onImageClick(memory.mediaUrl)}
                            showHoverEffect={true}
                            useMasonryLayout={true}
                            maxHeight={500}
                        />
                    ) : (
                        <div className="relative w-full mb-4 rounded-lg overflow-hidden pt-[56.25%]">
                            <iframe
                                src={`https://www.youtube.com/embed/${memory.youtubeId}`}
                                className="absolute top-0 left-0 w-full h-full"
                                allowFullScreen
                            />
                        </div>
                    )}

                    <h3 className="text-xl font-bold text-white mb-2">
                        <MatrixText text={memory.title}/>
                    </h3>

                    <div className="flex items-center gap-2 text-white/60 mb-4">
                        <Calendar className="w-4 h-4"/>
                        <span className="text-sm">{memory.date}</span>
                    </div>

                    <p className="text-white/80 text-sm leading-relaxed">
                        {memory.description}
                    </p>
                </div>
            </TerminalWindow>
        </motion.div>
    );
};

const MemoriesPage = () => {
    const {isAdmin} = useAuth();
    const {getMemories, deleteMemory} = useMemories();
    const [memories, setMemories] = useState<Memory[]>([]);
    const [selectedMemory, setSelectedMemory] = useState<Memory | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedImage, setSelectedImage] = useState<string | null>(null);

    useEffect(() => {
        loadMemories();
    }, []);

    const loadMemories = async () => {
        const data = await getMemories();
        setMemories(data);
    };

    const handleDelete = async (id: string) => {
        if (window.confirm('Are you sure you want to delete this memory?')) {
            await deleteMemory(id);
            await loadMemories();
        }
    };

    return (
        <BaseLayout>


            <div className="mb-8 flex justify-between items-center">
                <Link
                    href="/"
                    className="inline-flex items-center text-white/80 hover:text-white
                        transition-colors group bg-white/5 backdrop-blur-sm
                        px-4 py-2 rounded-full hover:bg-white/10"
                >
                    <ArrowLeft className="mr-2 w-4 h-4 group-hover:-translate-x-1 transition-transform"/>
                    <Logo className="w-6 h-6 text-white"/>
                    <span className="ml-2">
                        <MatrixText text="Return to Main System"/>
                    </span>
                </Link>

                {isAdmin && (
                    <button
                        onClick={() => setIsModalOpen(true)}
                        className="inline-flex items-center text-white/80 hover:text-white
                            transition-colors group bg-white/5 backdrop-blur-sm
                            px-4 py-2 rounded-full hover:bg-white/10"
                    >
                        <Plus className="mr-2 w-4 h-4"/>
                        <MatrixText text="Add Memory Fragment"/>
                    </button>
                )}
            </div>
            <div className="max-w-4xl mx-auto mb-12 text-center">
                <h1 className="text-4xl font-bold text-white mb-4">
                    <MatrixText text="Memory Archive"/>
                </h1>
                <p className="text-white/80 text-lg max-w-2xl mx-auto">
                    <MatrixText
                        text="Welcome to my digital sanctuary of professional moments. Here, I've curated fragments of my journey in a field I deeply love - each memory a testament to the passion that drives my work. These are the stories and milestones that shaped my path, carefully preserved in the matrix of time."/>
                </p>
            </div>
            <div className="css-masonry-grid max-w-6xl mx-auto">
                {memories.map((memory) => (
                    <div key={memory.id} className="masonry-item">
                        <MemoryCard
                            memory={memory}
                            isAdmin={isAdmin}
                            onEdit={() => {
                                setSelectedMemory(memory);
                                setIsModalOpen(true);
                            }}
                            onDelete={() => handleDelete(memory.id)}
                            onImageClick={(url) => setSelectedImage(url)}
                        />
                    </div>
                ))}
            </div>

            {isModalOpen && (
                <MemoryModal
                    memory={selectedMemory}
                    onClose={() => {
                        setSelectedMemory(null);
                        setIsModalOpen(false);
                        loadMemories();
                    }}
                />
            )}

            {selectedImage && (
                <ImageModal
                    imageUrl={selectedImage}
                    onClose={() => setSelectedImage(null)}
                />
            )}
        </BaseLayout>
    );
};

export default MemoriesPage;