'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { ArrowLeft, ExternalLink, Plus, Edit, Trash } from 'lucide-react';
import BaseLayout from '@/components/BaseLayout';
import Logo from "@/components/Logo";
import { MatrixText } from '@/components/matrix/MatrixText';
import { TerminalWindow } from '@/components/matrix/TerminalWindow';
import { MatrixBadge } from '@/components/matrix/MatrixBadge';
import { useAuth } from '@/context/AuthContext';
import { useProjects } from '@/hooks/useProjects';
import ProjectModal from '@/components/ProjectModal';

interface Project {
    id: string;
    title: string;
    description: string;
    tech: string[];
    link?: string;
    image: string;
    demoLink?: string;
}

const ProjectsPage = () => {
    const { isAdmin } = useAuth();
    const { getProjects, deleteProject } = useProjects();
    const [projects, setProjects] = useState<Project[]>([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedProject, setSelectedProject] = useState<Project | null>(null);

    const loadProjects = useCallback(async () => {
        try {
            const data = await getProjects();
            const projectsData: Project[] = data.map(doc => ({
                id: doc.id,
                title: doc.title || '',
                description: doc.description || '',
                tech: doc.tech || [],
                link: doc.link,
                image: doc.image || ''
            }));
            setProjects(projectsData);
        } catch (error) {
            console.error('Error loading projects:', error);
        }
    }, [getProjects]);

    useEffect(() => {
        loadProjects();
    }, [loadProjects]);

    const handleEdit = (project: Project) => {
        setSelectedProject(project);
        setIsModalOpen(true);
    };

    const handleDelete = async (id: string) => {
        if (window.confirm('Are you sure you want to delete this project?')) {
            await deleteProject(id);
            await loadProjects();
        }
    };

    const handleModalClose = () => {
        setSelectedProject(null);
        setIsModalOpen(false);
        loadProjects();
    };

    return (
        <BaseLayout>
            <Head>
                <title>Project Database</title>
                <meta name="description" content="Digital constructs and system implementations" />
                <script type="application/ld+json">
                    {JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "ItemList",
                        "itemListElement": projects.map((project, index) => ({
                            "@type": "ListItem",
                            "position": index + 1,
                            "url": project.link,
                            "name": project.title,
                            "description": project.description,
                            "image": project.image,
                            "additionalType": "https://schema.org/SoftwareApplication",
                            "applicationCategory": "WebApplication",
                            "operatingSystem": "All",
                            "softwareVersion": "1.0",
                            "offers": {
                                "@type": "Offer",
                                "price": "0.00",
                                "priceCurrency": "USD"
                            }
                        }))
                    })}
                </script>
            </Head>

            {/* Page Title & Actions */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-8"
            >
                <div className="flex justify-between items-center mb-8">
                    <Link
                        href="/"
                        className="inline-flex items-center text-white/80 hover:text-white
                            transition-colors group bg-white/5 backdrop-blur-sm
                            px-4 py-2 rounded-full hover:bg-white/10"
                    >
                        <ArrowLeft className="mr-2 w-4 h-4 group-hover:-translate-x-1 transition-transform" />
                        <Logo className="w-6 h-6 text-white" />
                        <span className="ml-2">
                            <MatrixText text="Return to Main System" />
                        </span>
                    </Link>

                    {isAdmin && (
                        <button
                            onClick={() => setIsModalOpen(true)}
                            className="inline-flex items-center text-white/80 hover:text-white
                                transition-colors group bg-white/5 backdrop-blur-sm
                                px-4 py-2 rounded-full hover:bg-white/10"
                        >
                            <Plus className="mr-2 w-4 h-4" />
                            <MatrixText text="Add New Project" />
                        </button>
                    )}
                </div>

                <h1 className="text-4xl font-bold text-white mb-4">
                    <MatrixText text="Project Database" />
                </h1>
                <p className="text-xl text-white/80">
                    <MatrixText text="Digital constructs and system implementations" />
                </p>
            </motion.div>

            {/* Project List */}
            <div className="grid gap-8">
                {projects.map((project, index) => (
                    <motion.div
                        key={project.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="relative group"
                    >
                        <TerminalWindow
                            title={`Project_${index + 1}.dmg`}
                            className="backdrop-blur-lg bg-white/10 rounded-3xl shadow-xl
                                hover:bg-white/[0.15] transition-all duration-300
                                hover:shadow-[0_0_30px_rgba(32,255,32,0.1)]"
                        >
                            {isAdmin && (
                                <div className="absolute top-2 right-2 z-10 flex gap-2">
                                    <button
                                        onClick={() => handleEdit(project)}
                                        className="p-2 rounded-full bg-white/10 hover:bg-white/20
                                            transition-colors opacity-0 group-hover:opacity-100"
                                    >
                                        <Edit className="w-4 h-4 text-white/80" />
                                    </button>
                                    <button
                                        onClick={() => handleDelete(project.id)}
                                        className="p-2 rounded-full bg-white/10 hover:bg-white/20
                                            transition-colors opacity-0 group-hover:opacity-100"
                                    >
                                        <Trash className="w-4 h-4 text-white/80" />
                                    </button>
                                </div>
                            )}

                            <div className="p-6">
                                {/* Project Image */}
                                <div className="relative w-full h-[300px] mb-6 rounded-xl overflow-hidden
                                    group-hover:shadow-[0_0_30px_rgba(32,255,32,0.2)] transition-shadow">
                                    <Image
                                        src={project.image}
                                        alt={project.title}
                                        fill
                                        className="object-cover"
                                    />
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"/>
                                </div>

                                {/* Project Title */}
                                <h2 className="text-2xl font-bold text-white mb-4" itemProp="name">
                                    <MatrixText text={project.title}/>
                                </h2>

                                {/* Project Description */}
                                <p className="text-white/80 mb-6" itemProp="description">
                                    {project.description}
                                </p>

                                {/* Project Technologies */}
                                <div className="flex flex-wrap gap-2 mb-6" itemProp="technologies">
                                    {project.tech.map(tech => (
                                        <MatrixBadge key={tech}>
                                            <MatrixText text={tech}/>
                                        </MatrixBadge>
                                    ))}
                                </div>

                                {/* Project Links */}
                                <div className="flex gap-4">
                                    {project.link && (
                                        <a
                                            href={project.link}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            itemProp="url"
                                            className="inline-flex items-center text-white/80 hover:text-white
                                                transition-colors hover:scale-105 active:scale-95"
                                        >
                                            <ExternalLink className="mr-2 w-4 h-4"/>
                                            <MatrixText text="View Project"/>
                                        </a>
                                    )}
                                    {project.demoLink && (
                                        <a
                                            href={project.demoLink}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="inline-flex items-center text-white/80 hover:text-white
                                                transition-colors hover:scale-105 active:scale-95"
                                        >
                                            <ExternalLink className="mr-2 w-4 h-4"/>
                                            <MatrixText text="Access Live Demo"/>
                                        </a>
                                    )}
                                </div>
                            </div>
                        </TerminalWindow>
                    </motion.div>
                ))}
            </div>

            {/* Project Modal */}
            {isModalOpen && (
                <ProjectModal
                    project={selectedProject}
                    onClose={handleModalClose}
                />
            )}
        </BaseLayout>
    );
};

export default ProjectsPage;