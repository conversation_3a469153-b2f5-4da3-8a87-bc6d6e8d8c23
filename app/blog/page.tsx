// app/blog/page.tsx
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { ArrowLeft, Clock, Calendar, Tag, Plus, Edit, Trash, Eye, EyeOff } from 'lucide-react';
import BaseLayout from '@/components/BaseLayout';
import Logo from "@/components/Logo";
import { MatrixText } from '@/components/matrix/MatrixText';
import { TerminalWindow } from '@/components/matrix/TerminalWindow';
import { TypewriterText } from '@/components/matrix/TypewriterText';
import { useAuth } from '@/context/AuthContext';
import { useBlogs } from '@/hooks/useBlogs';
import BlogModal from '@/components/BlogModal';
import { BlogPost } from '@/types/blog';

const BlogPage = () => {
    const { isAdmin } = useAuth();
    const { getBlogs, deleteBlog, updateBlog } = useBlogs();
    const [blogs, setBlogs] = useState<BlogPost[]>([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedBlog, setSelectedBlog] = useState<BlogPost | null>(null);

    const loadBlogs = useCallback(async () => {
        try {
            const data = await getBlogs();
            const filteredData = isAdmin ? data : data.filter(blog => blog.published);
            setBlogs(filteredData.sort((a, b) =>
                new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
            ));
        } catch (error) {
            console.error('Error loading blogs:', error);
        }
    }, [getBlogs, isAdmin]);

    useEffect(() => {
        loadBlogs();
    }, [loadBlogs]);

    const handlePublishToggle = async (blog: BlogPost) => {
        try {
            await updateBlog(blog.id, {
                ...blog,
                published: !blog.published
            });
            await loadBlogs();
        } catch (error) {
            console.error('Error toggling publish status:', error);
        }
    };

    const handleEdit = (blog: BlogPost) => {
        setSelectedBlog(blog);
        setIsModalOpen(true);
    };

    const handleDelete = async (id: string) => {
        if (window.confirm('Are you sure you want to delete this blog post?')) {
            await deleteBlog(id);
            await loadBlogs();
        }
    };

    const handleModalClose = () => {
        setSelectedBlog(null);
        setIsModalOpen(false);
        loadBlogs();
    };

    return (
        <BaseLayout>
            <Head>
                <title>Neural Network Logs | Miktad Tahir</title>
                <meta name="description" content="Digital thoughts decoded into binary brilliance. Explore technical insights and web development revelations." />
                <meta name="keywords" content="web development, programming, technical blog, coding insights" />
                <script type="application/ld+json">
                    {JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "Blog",
                        "headline": "Neural Network Logs",
                        "description": "Digital thoughts decoded into binary brilliance",
                        "publisher": {
                            "@type": "Person",
                            "name": "Miktad Tahir"
                        },
                        "blogPost": blogs.map(blog => ({
                            "@type": "BlogPosting",
                            "headline": blog.title,
                            "description": blog.excerpt,
                            "datePublished": blog.createdAt,
                            "dateModified": blog.updatedAt,
                            "author": {
                                "@type": "Person",
                                "name": "Miktad Tahir"
                            },
                            "image": blog.image,
                            "keywords": blog.tags.join(", ")
                        }))
                    })}
                </script>
            </Head>

            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-8"
            >
                <div className="flex justify-between items-center mb-8">
                    <Link
                        href="/"
                        className="inline-flex items-center text-white/80 hover:text-white
                            transition-colors group bg-white/5 backdrop-blur-sm
                            px-4 py-2 rounded-full hover:bg-white/10"
                    >
                        <ArrowLeft className="mr-2 w-4 h-4 group-hover:-translate-x-1 transition-transform" />
                        <Logo className="w-6 h-6 text-white" />
                        <span className="ml-2">
                            <MatrixText text="Return to Main System" />
                        </span>
                    </Link>

                    {isAdmin && (
                        <button
                            onClick={() => setIsModalOpen(true)}
                            className="inline-flex items-center text-white/80 hover:text-white
                                transition-colors group bg-white/5 backdrop-blur-sm
                                px-4 py-2 rounded-full hover:bg-white/10"
                        >
                            <Plus className="mr-2 w-4 h-4" />
                            <MatrixText text="Initialize New Log" />
                        </button>
                    )}
                </div>

                <h1 className="text-4xl font-bold text-white mb-4">
                    <MatrixText text="Neural Network Logs" />
                </h1>
                <TypewriterText
                    text="Digital thoughts decoded into binary brilliance"
                    className="text-xl text-white/80 block mb-12"
                />
            </motion.div>

            <div className="grid gap-8">
                {blogs.map((blog, index) => (
                    <motion.div
                        key={blog.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="relative group"
                    >
                        <TerminalWindow
                            title={`${blog.published ? 'Log' : 'Draft'}_${blog.slug}.dmg`}
                            className={`
                                backdrop-blur-lg bg-white/10 rounded-3xl shadow-xl
                                hover:bg-white/[0.15] transition-all duration-300
                                hover:shadow-[0_0_30px_rgba(32,255,32,0.1)]
                                ${!blog.published ? 'opacity-60' : ''}
                            `}
                        >
                            {isAdmin && (
                                <div className="absolute top-2 right-2 z-10 flex gap-2">
                                    <button
                                        onClick={() => handlePublishToggle(blog)}
                                        className="p-2 rounded-full bg-white/10 hover:bg-white/20
                                            transition-colors opacity-0 group-hover:opacity-100"
                                        title={blog.published ? 'Unpublish' : 'Publish'}
                                    >
                                        {blog.published ? (
                                            <EyeOff className="w-4 h-4 text-white/80" />
                                        ) : (
                                            <Eye className="w-4 h-4 text-white/80" />
                                        )}
                                    </button>
                                    <button
                                        onClick={() => handleEdit(blog)}
                                        className="p-2 rounded-full bg-white/10 hover:bg-white/20
                                            transition-colors opacity-0 group-hover:opacity-100"
                                    >
                                        <Edit className="w-4 h-4 text-white/80" />
                                    </button>
                                    <button
                                        onClick={() => handleDelete(blog.id)}
                                        className="p-2 rounded-full bg-white/10 hover:bg-white/20
                                            transition-colors opacity-0 group-hover:opacity-100"
                                    >
                                        <Trash className="w-4 h-4 text-white/80" />
                                    </button>
                                </div>
                            )}

                            <Link href={`/blog/${blog.slug}`}>
                                <div className="p-6">
                                    <div className="relative w-full h-[400px] mb-6 rounded-xl overflow-hidden
                                        group-hover:shadow-[0_0_30px_rgba(32,255,32,0.2)] transition-shadow"
                                    >
                                        <Image
                                            src={blog.image}
                                            alt={blog.title}
                                            fill
                                            className="object-cover"
                                        />
                                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                                    </div>

                                    <h2 className="text-2xl font-bold text-white mb-4 group-hover:text-[#00ff00] transition-colors">
                                        <MatrixText text={blog.title} />
                                    </h2>

                                    <div className="flex items-center gap-4 text-white/60 mb-4">
                                        <div className="flex items-center gap-1">
                                            <Calendar className="w-4 h-4" />
                                            <span className="text-sm">{blog.date}</span>
                                        </div>
                                        <div className="flex items-center gap-1">
                                            <Clock className="w-4 h-4" />
                                            <span className="text-sm">{blog.readTime}</span>
                                        </div>
                                    </div>

                                    <p className="text-white/80 mb-6">
                                        {blog.excerpt}
                                    </p>

                                    <div className="flex flex-wrap gap-2">
                                        {blog.tags.map(tag => (
                                            <span
                                                key={tag}
                                                className="inline-flex items-center gap-1 text-sm text-white/60
                                                    bg-white/5 px-3 py-1 rounded-full"
                                            >
                                                <Tag className="w-3 h-3" />
                                                {tag}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            </Link>
                        </TerminalWindow>
                    </motion.div>
                ))}
            </div>

            {isModalOpen && (
                <BlogModal
                    blog={selectedBlog}
                    onClose={handleModalClose}
                />
            )}
        </BaseLayout>
    );
};

export default BlogPage;