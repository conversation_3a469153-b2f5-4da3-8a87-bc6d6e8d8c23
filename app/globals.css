
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 17, 23, 35;
  --background-end-rgb: 0, 0, 0;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
          to bottom,
          rgb(var(--background-start-rgb)),
          rgb(var(--background-end-rgb))
  );
  min-height: 100vh;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Webkit scrollbar customization */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
}

/* Fade in animation utility */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}
@font-face {
    font-family: 'GeistMono';
    src: url('/fonts/GeistMonoVF.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Geist';
    src: url('/fonts/GeistVF.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

body {
    font-family: 'Geist', 'GeistMono', sans-serif;
}

/* Custom image utilities for memory archive */
.memory-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
}

.memory-image-portrait {
    aspect-ratio: 3/4;
    max-height: 20rem;
}

.memory-image-landscape {
    aspect-ratio: 4/3;
    max-height: 16rem;
}

.memory-image-square {
    aspect-ratio: 1/1;
    max-height: 16rem;
}

.memory-image-wide {
    aspect-ratio: 16/9;
    max-height: 12rem;
}

/* Image modal improvements */
.image-modal-backdrop {
    backdrop-filter: blur(8px);
    background: rgba(0, 0, 0, 0.9);
}

/* Smooth image loading */
.memory-image {
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.memory-image:hover {
    transform: scale(1.02);
}

/* Loading state for images */
.image-loading {
    background: linear-gradient(90deg, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}