// components/MemoryModal.tsx
'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { X, Upload, Loader, Film, Image as ImageIcon } from 'lucide-react';
import { useMemories } from '@/hooks/useMemories';
import { MatrixText } from './matrix/MatrixText';
import { TerminalWindow } from './matrix/TerminalWindow';
import { Memory } from '@/types/memory';
import { AdaptiveImage } from './AdaptiveImage';

interface MemoryModalProps {
    memory: Memory | null;
    onClose: () => void;
}

interface FormData {
    title: string;
    description: string;
    date: string;
    mediaType: 'image' | 'video';
    mediaUrl: string;
}

const MemoryModal: React.FC<MemoryModalProps> = ({ memory, onClose }) => {
    const { addMemory, updateMemory } = useMemories();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [imageFile, setImageFile] = useState<File | undefined>(undefined);
    const [imagePreview, setImagePreview] = useState(memory?.mediaType === 'image' ? memory.mediaUrl : '');

    const [formData, setFormData] = useState<FormData>({
        title: memory?.title || '',
        description: memory?.description || '',
        date: memory?.date || new Date().toISOString().split('T')[0],
        mediaType: memory?.mediaType || 'image',
        mediaUrl: memory?.mediaUrl || ''
    });

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            if (file.size > 5 * 1024 * 1024) {
                setError('Image size should be less than 5MB');
                return;
            }
            setImageFile(file);
            const reader = new FileReader();
            reader.onloadend = () => {
                setImagePreview(reader.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        setError(null);

        try {
            if (!formData.title) throw new Error('Title is required');
            if (!formData.description) throw new Error('Description is required');
            if (!formData.date) throw new Error('Date is required');

            if (formData.mediaType === 'image' && !imageFile && !memory?.mediaUrl) {
                throw new Error('Image is required');
            }

            if (formData.mediaType === 'video' && !formData.mediaUrl) {
                throw new Error('YouTube URL is required');
            }

            if (memory?.id) {
                await updateMemory(memory.id, {
                    ...formData,
                    mediaUrl: formData.mediaType === 'video' ? formData.mediaUrl : memory.mediaUrl
                }, imageFile);
            } else {
                await addMemory({
                    ...formData,
                    mediaUrl: formData.mediaType === 'video' ? formData.mediaUrl : ''
                }, imageFile);
            }
            onClose();
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to save memory');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
            <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="w-full max-w-2xl max-h-[90vh] overflow-y-auto"
            >
                <TerminalWindow
                    title={`${memory ? 'Update' : 'New'}_Memory.dmg`}
                    className="backdrop-blur-lg bg-white/10 rounded-3xl shadow-xl"
                >
                    <form onSubmit={handleSubmit} className="p-6 space-y-6">
                        <div className="flex justify-between items-center">
                            <h2 className="text-2xl font-bold text-white">
                                <MatrixText text={memory ? 'Update Memory Fragment' : 'Initialize New Memory'} />
                            </h2>
                            <button
                                type="button"
                                onClick={onClose}
                                className="p-2 rounded-full hover:bg-white/10 transition-colors"
                            >
                                <X className="w-5 h-5 text-white/60" />
                            </button>
                        </div>

                        {/* Media Type Selection */}
                        <div className="flex gap-4">
                            <button
                                type="button"
                                onClick={() => setFormData(prev => ({ ...prev, mediaType: 'image' }))}
                                className={`flex items-center gap-2 px-4 py-2 rounded-full transition-colors
                                    ${formData.mediaType === 'image'
                                    ? 'bg-[#00ff00]/20 text-[#00ff00]'
                                    : 'bg-white/5 text-white/60 hover:bg-white/10'
                                }`}
                            >
                                <ImageIcon className="w-4 h-4" />
                                Image
                            </button>
                            <button
                                type="button"
                                onClick={() => setFormData(prev => ({ ...prev, mediaType: 'video' }))}
                                className={`flex items-center gap-2 px-4 py-2 rounded-full transition-colors
                                    ${formData.mediaType === 'video'
                                    ? 'bg-[#00ff00]/20 text-[#00ff00]'
                                    : 'bg-white/5 text-white/60 hover:bg-white/10'
                                }`}
                            >
                                <Film className="w-4 h-4" />
                                YouTube Video
                            </button>
                        </div>

                        {/* Media Upload/URL */}
                        {formData.mediaType === 'image' ? (
                            <div className="relative">
                                <div
                                    className={`
                                        w-full rounded-xl overflow-hidden
                                        ${!imagePreview ? 'h-48 border-2 border-dashed border-white/20' : 'min-h-48 max-h-80'}
                                    `}
                                >
                                    {imagePreview ? (
                                        <AdaptiveImage
                                            src={imagePreview}
                                            alt="Preview"
                                            className="w-full"
                                            useMasonryLayout={false}
                                            maxHeight={320}
                                        />
                                    ) : (
                                        <div className="flex flex-col items-center justify-center h-full text-white/60">
                                            <Upload className="w-8 h-8 mb-2" />
                                            <span>Upload Image</span>
                                        </div>
                                    )}
                                </div>
                                <input
                                    type="file"
                                    onChange={handleImageChange}
                                    accept="image/*"
                                    className="absolute inset-0 opacity-0 cursor-pointer"
                                />
                            </div>
                        ) : (
                            <div>
                                <label className="text-white/60 text-sm">YouTube URL</label>
                                <input
                                    type="url"
                                    value={formData.mediaUrl}
                                    onChange={e => setFormData(prev => ({ ...prev, mediaUrl: e.target.value }))}
                                    className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white mt-1"
                                    placeholder="https://www.youtube.com/watch?v=..."
                                />
                            </div>
                        )}

                        {/* Form Fields */}
                        <div className="space-y-4">
                            <div>
                                <label className="text-white/60 text-sm">Title</label>
                                <input
                                    type="text"
                                    value={formData.title}
                                    onChange={e => setFormData(prev => ({ ...prev, title: e.target.value }))}
                                    className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white mt-1"
                                    placeholder="Enter memory title"
                                />
                            </div>

                            <div>
                                <label className="text-white/60 text-sm">Date</label>
                                <input
                                    type="date"
                                    value={formData.date}
                                    onChange={e => setFormData(prev => ({ ...prev, date: e.target.value }))}
                                    className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white mt-1"
                                />
                            </div>

                            <div>
                                <label className="text-white/60 text-sm">Description</label>
                                <textarea
                                    value={formData.description}
                                    onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
                                    className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white mt-1 h-32"
                                    placeholder="Enter memory description"
                                />
                            </div>
                        </div>

                        {error && (
                            <div className="text-red-500 text-sm">
                                {error}
                            </div>
                        )}

                        <div className="flex justify-end pt-4">
                            <button
                                type="submit"
                                disabled={loading}
                                className="inline-flex items-center px-6 py-2 rounded-full
                                    bg-white/10 hover:bg-white/20 text-white transition-colors"
                            >
                                {loading ? (
                                    <>
                                        <Loader className="w-4 h-4 mr-2 animate-spin" />
                                        Processing...
                                    </>
                                ) : (
                                    <MatrixText text={memory ? 'Update Memory' : 'Store Memory'} />
                                )}
                            </button>
                        </div>
                    </form>
                </TerminalWindow>
            </motion.div>
        </div>
    );
};

export default MemoryModal;