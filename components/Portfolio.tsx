'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Github, Linkedin, Mail, ArrowRight, Twitter } from 'lucide-react';
import {FaStackOverflow } from 'react-icons/fa';
import BaseLayout from '@/components/BaseLayout';
import { MatrixText } from '@/components/matrix/MatrixText';
import { TerminalWindow } from '@/components/matrix/TerminalWindow';
import { TypewriterText } from '@/components/matrix/TypewriterText';
import { MatrixBadge } from '@/components/matrix/MatrixBadge';
import { GlowButton } from '@/components/matrix/GlowButton';
import Logo from "@/components/Logo";
import { SecureChannel } from "@/components/matrix/SecureChannel";
import { TestimonialSlider } from "@/components/matrix/TestimonialSlider";
import { SecretPlace } from '@/components/matrix/SecretPlace';


const Portfolio = () => {
    const frontendSkills = ['React', 'Next.js', 'TypeScript', 'Flutter'];
    const backendSkills = ['Node.js', 'Express', 'Firebase', 'MongoDB'];
    const toolsSkills = ['Git', 'Docker', 'AWS'];
    const securitySkills = ['OWASP', 'JWT', 'OAuth', 'SSL/TLS'];
    const [showSecretPlace, setShowSecretPlace] = useState(false);

    return (
        <BaseLayout>
            {showSecretPlace && <SecretPlace onClose={() => setShowSecretPlace(false)} />}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center mb-12 pt-8"
            >
                <motion.div
                    className="relative inline-block mb-4"
                    animate={{
                        boxShadow: ["0 0 0 rgba(32,255,32,0)", "0 0 30px rgba(32,255,32,0.3)", "0 0 0 rgba(32,255,32,0)"]
                    }}
                    transition={{
                        duration: 2,
                        repeat: Infinity,
                        repeatType: "reverse"
                    }}
                >
                    <Logo className="w-16 h-16 mx-auto" />
                </motion.div>

                <h1 className="text-6xl font-bold text-white mb-4">
                    <MatrixText text="Miktad Tahir" />
                </h1>

                <TypewriterText
                    text="Developer & Micropreneur"
                    className="text-xl text-white/80 mb-8 block"
                />

                <div className="flex space-x-6 justify-center">
                    <a
                        href="https://github.com/miktadtahir"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-white/80 hover:text-white transition-colors"
                    >
                        <Github className="w-6 h-6" />
                    </a>
                    <a
                        href="https://stackoverflow.com/users/15728644"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-white/80 hover:text-white transition-colors"
                    >
                        <FaStackOverflow className="w-6 h-6" />
                    </a>
                    <a
                        href="https://linkedin.com/in/miktadtahirdurak"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-white/80 hover:text-white transition-colors"
                    >
                        <Linkedin className="w-6 h-6" />
                    </a>
                    <a
                        href="https://twitter.com/miktadtahir"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-white/80 hover:text-white transition-colors"
                    >
                        <Twitter className="w-6 h-6" />
                    </a>

                    <a
                        href="mailto:<EMAIL>"
                        className="text-white/80 hover:text-white transition-colors"
                    >
                        <Mail className="w-6 h-6" />
                    </a>
                </div>
            </motion.div>

            {/* Cards Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* About Card */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="relative group lg:col-span-1"
                >
                    <TerminalWindow
                        title="About_Me.dmg"
                        className="backdrop-blur-lg bg-white/10 hover:bg-white/[0.15]
        transition-all duration-300 rounded-3xl p-6 shadow-xl
        hover:shadow-[0_0_30px_rgba(32,255,32,0.1)]"
                    >
                        <h2 className="text-2xl font-bold text-white mb-4">Venture, Innovate, Execute</h2>
                        <p className="text-white/80">
                            <MatrixText text="Enterprise protocol initialized. Entrepreneur identity confirmed." />
                            <br /><br />
                            Digital entrepreneur with a decade of experience navigating the venture matrices
                            of modern innovation. Architecting business realities at the intersection of
                            technological advancement and market disruption.
                            <br /><br />
                            My enterprise protocols span across multiple digital domains,
                            forging strategic connections between innovation concepts and market execution.
                            Together, we&apos;ll decode the future of digital entrepreneurship.
                        </p>
                    </TerminalWindow>
                </motion.div>

                {/* Skills Card */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="relative group lg:col-span-1"
                >
                    <TerminalWindow
                        title="Skills.dmg"
                        className="backdrop-blur-lg bg-white/10 hover:bg-white/[0.15]
transition-all duration-300 rounded-3xl p-6 shadow-xl
hover:shadow-[0_0_30px_rgba(32,255,32,0.1)]"
                    >
                        <h2 className="text-2xl font-bold text-white mb-4">Venture Tech Stack</h2>
                        <div className="space-y-4">
                            <div>
                                <h3 className="text-lg font-semibold text-white mb-2">Frontend Innovation</h3>
                                <div className="flex flex-wrap gap-2">
                                    {frontendSkills.map(skill => (
                                        <MatrixBadge key={skill}>
                                            <MatrixText text={skill} />
                                        </MatrixBadge>
                                    ))}
                                </div>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold text-white mb-2">Backend Architecture</h3>
                                <div className="flex flex-wrap gap-2">
                                    {backendSkills.map(skill => (
                                        <MatrixBadge key={skill}>
                                            <MatrixText text={skill} />
                                        </MatrixBadge>
                                    ))}
                                </div>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold text-white mb-2">Enterprise Tools</h3>
                                <div className="flex flex-wrap gap-2">
                                    {toolsSkills.map(skill => (
                                        <MatrixBadge key={skill}>
                                            <MatrixText text={skill} />
                                        </MatrixBadge>
                                    ))}
                                </div>
                            </div>


                            <div>
                                <h3 className="text-lg font-semibold text-white mb-2">Security Framework</h3>
                                <div className="flex flex-wrap gap-2">
                                    {securitySkills.map(skill => (
                                        <MatrixBadge key={skill}>
                                            <MatrixText text={skill} />
                                        </MatrixBadge>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </TerminalWindow>
                </motion.div>
                {/* Contact Card */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="relative group lg:col-span-1"
                >
                    <TerminalWindow
                        title="Contact.dmg"
                        className="backdrop-blur-lg bg-white/10 hover:bg-white/[0.15]
          transition-all duration-300 rounded-3xl p-6 shadow-xl"
                    >
                        <h2 className="text-2xl font-bold text-white mb-4">Network Protocol</h2>
                        <p className="text-white/80 mb-6">
                            Ready to collaborate? Connect to my network and let&apos;s explore venture opportunities together.
                        </p>
                        <div className="space-y-6">
                            <GlowButton href="mailto:<EMAIL>">
                                Initiate Partnership
                            </GlowButton>

                            <div className="space-y-3">
                                <div className="text-sm text-white/50 font-mono">Available Secure Channels:</div>
                                <div className="flex flex-wrap gap-3">
                                    <SecureChannel
                                        number="+905334207792"
                                        type="phone"
                                    />
                                    <SecureChannel
                                        number="+447383217442"
                                        type="whatsapp"
                                    />
                                </div>
                                <div className="text-xs text-white/40 font-mono">
                                    * Hover to decrypt secure channels
                                </div>
                            </div>
                        </div>
                    </TerminalWindow>
                </motion.div>
                {/* Projects Card */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className="relative group  lg:col-span-1"
                >
                    <TerminalWindow
                        title="Enterprises.dmg"
                        className="backdrop-blur-lg bg-white/10 hover:bg-white/[0.15]
              transition-all duration-300 rounded-3xl p-6 shadow-xl
              hover:shadow-[0_0_30px_rgba(32,255,32,0.1)]"
                    >
                        <h2 className="text-2xl font-bold text-white mb-4">Enterprise Matrix</h2>
                        <p className="text-white/80 mb-4">
                            Initialize connection to explore my digital ventures and entrepreneurial constructs.
                        </p>
                        <Link
                            href="/projects"
                            className="inline-flex items-center text-white hover:text-white/80 transition-colors group"
                        >
                            <MatrixText text="Access Enterprise Database" />
                            <ArrowRight
                                className="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform" />
                        </Link>

                    </TerminalWindow>
                </motion.div>

                {/* Blog Card */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                    className="relative group  lg:col-span-1"
                >
                    <TerminalWindow
                        title="Blog.dmg"
                        className="backdrop-blur-lg bg-white/10 hover:bg-white/[0.15]
          transition-all duration-300 rounded-3xl p-6 shadow-xl
          hover:shadow-[0_0_30px_rgba(32,255,32,0.1)]"
                    >
                        <h2 className="text-2xl font-bold text-white mb-4">Innovation Logs</h2>
                        <p className="text-white/80 mb-4">
                            Access my entrepreneurial insights. Venture strategies, market analysis, and innovation frameworks.
                        </p>
                        <Link
                            href="/blog"
                            className="inline-flex items-center text-white hover:text-white/80 transition-colors group"
                        >
                            <MatrixText text="Access Innovation Database" />
                            <ArrowRight
                                className="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform" />
                        </Link>
                    </TerminalWindow>
                </motion.div>
                {/* Memories Card */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                    className="relative group  lg:col-span-1"
                >
                    <TerminalWindow
                        title="Memories.dmg"
                        className="backdrop-blur-lg bg-white/10 hover:bg-white/[0.15]
          transition-all duration-300 rounded-3xl p-6 shadow-xl
          hover:shadow-[0_0_30px_rgba(32,255,32,0.1)]"
                    >
                        <h2 className="text-2xl font-bold text-white mb-4">Venture Chronicles</h2>
                        <p className="text-white/80 mb-4">
                            Access the entrepreneurial journey. Visual documentation of venture milestones.
                        </p>
                        <Link
                            href="/memories"
                            className="inline-flex items-center text-white hover:text-white/80 transition-colors group"
                        >
                            <MatrixText text="Access Venture Archive" />
                            <ArrowRight
                                className="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform" />
                        </Link>
                    </TerminalWindow>
                </motion.div>
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7 }}
                    className="relative group col-span-full"
                >
                    <TerminalWindow
                        title="User_Reviews.dmg"
                        className="backdrop-blur-lg bg-white/10 hover:bg-white/[0.15]
            transition-all duration-300 rounded-3xl p-6 shadow-xl
            hover:shadow-[0_0_30px_rgba(32,255,32,0.1)]"
                    >
                        <h2 className="text-2xl font-bold text-white mb-8">
                            <MatrixText text="Partnership Testimonials" />
                        </h2>

                        <TestimonialSlider />

                        {/* Matrix Background Effect */}
                        <div className="absolute inset-0 -z-10 overflow-hidden rounded-3xl opacity-10">
                            <div className="absolute inset-0 bg-gradient-to-r from-[#00ff00]/10 via-transparent to-[#00ff00]/10
                animate-matrix-scan-horizontal"/>
                        </div>
                    </TerminalWindow>
                </motion.div>
            </div>

            {/* Secret Access Point */}
            <div className="mt-8 text-center">
                <button
                    className="text-[#00ff00]/30 hover:text-[#00ff00] transition-colors duration-300 text-sm font-mono"
                    onClick={() => setShowSecretPlace(true)}
                >
                    <MatrixText text="Follow the white rabbit..." />
                </button>
            </div>
        </BaseLayout>
    );
};

export default Portfolio;