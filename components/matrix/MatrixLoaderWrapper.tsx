'use client';

import React, { useState, useEffect } from 'react';
import { MatrixLoader } from './MatrixLoader';

interface MatrixLoaderWrapperProps {
    children: React.ReactNode;
}

export const MatrixLoaderWrapper: React.FC<MatrixLoaderWrapperProps> = ({ children }) => {
    const [isLoading, setIsLoading] = useState(true);


    useEffect(() => {
        // Check if loader has been shown in this session
        const loaderShown = sessionStorage.getItem('matrixLoaderShown');
        
        if (loaderShown) {
            // If already shown in this session, skip loader
            setIsLoading(false);
        } else {
            // Show loader for first visit in session
            setIsLoading(true);
        }
    }, []);

    const handleLoaderComplete = () => {
        // Mark loader as shown in this session
        sessionStorage.setItem('matrixLoaderShown', 'true');
        setIsLoading(false);
        
        // Trigger audio context unlock
        const unlockAudio = () => {
            const audio = new Audio();
            audio.play().then(() => {
                audio.pause();
                console.log('Audio context unlocked via loader');
            }).catch(() => {
                console.log('Audio unlock failed');
            });
        };
        
        // Small delay to ensure smooth transition
        setTimeout(unlockAudio, 500);
    };

    if (isLoading) {
        return <MatrixLoader onComplete={handleLoaderComplete} />;
    }

    return <>{children}</>;
};
