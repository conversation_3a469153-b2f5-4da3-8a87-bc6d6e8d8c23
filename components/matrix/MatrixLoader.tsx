'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MatrixText } from './MatrixText';
import { TypewriterText } from './TypewriterText';
import Logo from '@/components/Logo';
import SubtleMatrixRain from '@/components/SubtleMatrixRain';

interface MatrixLoaderProps {
    onComplete: () => void;
}

export const MatrixLoader: React.FC<MatrixLoaderProps> = ({ onComplete }) => {
    const [currentStep, setCurrentStep] = useState(0);
    const [isComplete, setIsComplete] = useState(false);
    const [audioUnlocked, setAudioUnlocked] = useState(false);
    const [showClickPrompt, setShowClickPrompt] = useState(false);

    const loadingSteps = useMemo(() => [
        "Initializing Enterprise Protocol...",
        "Establishing Secure Network...",
        "Loading Venture Database...",
        "Activating Innovation Matrix...",
        "Welcome to the Digital Realm."
    ], []);

    // Using site's original SubtleMatrixRain component

    // Scanning effect matching site's animations
    const ScanningEffect = () => (
        <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-[#00ff00]/10 to-transparent"
            animate={{ x: ['-100%', '100%'] }}
            transition={{
                duration: 2,
                repeat: Infinity,
                ease: "linear"
            }}
        />
    );

    // Auto-progress through steps
    useEffect(() => {
        if (currentStep < loadingSteps.length - 1) {
            const timer = setTimeout(() => {
                setCurrentStep(prev => prev + 1);
            }, 1500);
            return () => clearTimeout(timer);
        } else {
            // Show click prompt after final step
            const timer = setTimeout(() => {
                setShowClickPrompt(true);
            }, 1000);
            return () => clearTimeout(timer);
        }
    }, [currentStep, loadingSteps.length]);

    const handleClick = async () => {
        console.log('Click detected!', { showClickPrompt, audioUnlocked });

        if (!showClickPrompt) {
            console.log('Click ignored - prompt not shown');
            return;
        }

        console.log('Processing click...');

        try {
            // Try to unlock audio context
            if (!audioUnlocked) {
                console.log('Attempting to unlock audio...');
                const audio = new Audio();
                await audio.play();
                audio.pause();
                setAudioUnlocked(true);
                console.log('Audio unlocked successfully');
            }

            // Complete the loading process
            console.log('Completing loader...');
            setIsComplete(true);
            setTimeout(() => {
                console.log('Calling onComplete...');
                onComplete();
            }, 600);
        } catch (error) {
            // Even if audio fails, continue to the site
            console.log('Audio failed, but continuing:', error);
            setIsComplete(true);
            setTimeout(() => {
                console.log('Calling onComplete after error...');
                onComplete();
            }, 600);
        }
    };

    return (
        <AnimatePresence>
            {!isComplete && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.5 }}
                    className="fixed inset-0 z-50 flex items-center justify-center cursor-pointer"
                    onClick={handleClick}
                >
                    <SubtleMatrixRain />
                    <ScanningEffect />

                    {/* Main content - matching site's card style */}
                    <div className="relative z-10 text-center max-w-2xl mx-auto px-8">
                        {/* Logo section - matching site's logo */}
                        <motion.div
                            initial={{ scale: 0, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ duration: 0.8, ease: "easeOut" }}
                            className="mb-12"
                        >
                            <motion.div
                                className="relative inline-block mb-6"
                                animate={{
                                    boxShadow: ["0 0 0 rgba(0,255,0,0)", "0 0 30px rgba(0,255,0,0.3)", "0 0 0 rgba(0,255,0,0)"]
                                }}
                                transition={{
                                    duration: 2,
                                    repeat: Infinity,
                                    repeatType: "reverse"
                                }}
                            >
                                <Logo className="w-20 h-20 mx-auto" />
                            </motion.div>

                            <h1 className="text-5xl font-bold text-white mb-4">
                                <MatrixText text="Miktad Tahir" />
                            </h1>

                            <TypewriterText
                                text="Developer & Micropreneur"
                                className="text-lg text-white/80 mb-8 block"
                            />
                        </motion.div>

                        {/* Loading Steps - matching site's terminal style */}
                        <motion.div
                            initial={{ y: 20, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ duration: 0.8, delay: 0.3 }}
                            className="backdrop-blur-lg bg-white/10 rounded-3xl p-8 mb-8 shadow-xl"
                        >
                            <div className="space-y-4">
                                {loadingSteps.map((step, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, x: -20 }}
                                        animate={{
                                            opacity: index <= currentStep ? 1 : 0.3,
                                            x: index <= currentStep ? 0 : -20
                                        }}
                                        transition={{ duration: 0.5 }}
                                        className="flex items-center justify-between"
                                    >
                                        <div className="flex items-center space-x-4">
                                            <motion.div
                                                animate={index === currentStep ? {
                                                    rotate: 360,
                                                    scale: [1, 1.2, 1]
                                                } : {}}
                                                transition={{
                                                    rotate: { duration: 1, repeat: Infinity, ease: "linear" },
                                                    scale: { duration: 0.5, repeat: Infinity }
                                                }}
                                                className={`w-3 h-3 border-2 rounded-full ${
                                                    index < currentStep
                                                        ? 'border-[#00ff00] bg-[#00ff00]'
                                                        : index === currentStep
                                                            ? 'border-[#00ff00] border-t-transparent'
                                                            : 'border-white/30'
                                                }`}
                                            />
                                            <div className="text-white font-mono text-sm">
                                                {index === currentStep ? (
                                                    <TypewriterText
                                                        text={step}
                                                        className="text-white"
                                                    />
                                                ) : index < currentStep ? (
                                                    <span className="text-white">{step}</span>
                                                ) : (
                                                    <span className="text-white/40">{step}</span>
                                                )}
                                            </div>
                                        </div>
                                        {index < currentStep && (
                                            <motion.div
                                                initial={{ scale: 0 }}
                                                animate={{ scale: 1 }}
                                                className="text-[#00ff00] text-lg"
                                            >
                                                ✓
                                            </motion.div>
                                        )}
                                    </motion.div>
                                ))}
                            </div>

                            {/* Progress Bar */}
                            <motion.div
                                className="mt-6 w-full"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ delay: 0.5 }}
                            >
                                <div className="w-full bg-white/10 rounded-full h-2 overflow-hidden">
                                    <motion.div
                                        className="h-full bg-gradient-to-r from-[#00ff00] to-[#00ff00]/80"
                                        initial={{ width: 0 }}
                                        animate={{ width: `${((currentStep + 1) / loadingSteps.length) * 100}%` }}
                                        transition={{ duration: 0.8, ease: "easeOut" }}
                                    />
                                </div>
                                <div className="text-center mt-2 text-white/70 font-mono text-xs">
                                    {Math.round(((currentStep + 1) / loadingSteps.length) * 100)}% COMPLETE
                                </div>
                            </motion.div>
                        </motion.div>

                        {/* Click to Continue - matching site's button style */}
                        <AnimatePresence>
                            {showClickPrompt && (
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    exit={{ opacity: 0, y: -20 }}
                                    transition={{ duration: 0.5 }}
                                    className="text-center"
                                >
                                    <motion.div
                                        className="relative group inline-flex items-center px-8 py-3 rounded-full bg-white/20 hover:bg-white/30 text-white transition-all hover:shadow-[0_0_20px_rgba(0,255,0,0.3)] hover:scale-105"
                                        animate={{
                                            opacity: [0.8, 1, 0.8],
                                            boxShadow: [
                                                "0 0 0 rgba(0,255,0,0)",
                                                "0 0 20px rgba(0,255,0,0.3)",
                                                "0 0 0 rgba(0,255,0,0)"
                                            ]
                                        }}
                                        transition={{ duration: 2, repeat: Infinity }}
                                    >
                                        <MatrixText text="Click to Enter the Matrix" />

                                        {/* Scanning effect */}
                                        <motion.div
                                            className="absolute inset-0 bg-gradient-to-r from-transparent via-[#00ff00]/20 to-transparent rounded-full"
                                            animate={{ x: ['-100%', '100%'] }}
                                            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                                        />
                                    </motion.div>
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};
