'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface MatrixLoaderProps {
    onComplete: () => void;
}

export const MatrixLoader: React.FC<MatrixLoaderProps> = ({ onComplete }) => {
    const [progress, setProgress] = useState(0);
    const [currentPhase, setCurrentPhase] = useState(0);
    const [isComplete, setIsComplete] = useState(false);
    const [audioUnlocked, setAudioUnlocked] = useState(false);
    const [showClickPrompt, setShowClickPrompt] = useState(false);

    const phases = useMemo(() => [
        { name: "NEURAL_NETWORK_INIT", duration: 1500 },
        { name: "QUANTUM_ENCRYPTION", duration: 1200 },
        { name: "BLOCKCHAIN_SYNC", duration: 1800 },
        { name: "AI_CONSCIOUSNESS", duration: 1000 },
        { name: "REALITY_MATRIX", duration: 800 }
    ], []);

    // Geometric patterns for background
    const GeometricBackground = () => (
        <div className="absolute inset-0 overflow-hidden">
            {/* Animated grid */}
            <div className="absolute inset-0 opacity-10">
                <div className="grid grid-cols-12 grid-rows-12 h-full w-full">
                    {Array.from({ length: 144 }).map((_, i) => (
                        <motion.div
                            key={i}
                            className="border border-white/20"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: [0, 0.5, 0] }}
                            transition={{
                                duration: 2,
                                delay: i * 0.02,
                                repeat: Infinity,
                                repeatDelay: 3
                            }}
                        />
                    ))}
                </div>
            </div>

            {/* Floating geometric shapes */}
            {Array.from({ length: 20 }).map((_, i) => (
                <motion.div
                    key={i}
                    className="absolute w-2 h-2 bg-white/20"
                    style={{
                        left: `${Math.random() * 100}%`,
                        top: `${Math.random() * 100}%`,
                    }}
                    animate={{
                        x: [0, Math.random() * 200 - 100],
                        y: [0, Math.random() * 200 - 100],
                        rotate: [0, 360],
                        scale: [1, 1.5, 1],
                    }}
                    transition={{
                        duration: Math.random() * 10 + 5,
                        repeat: Infinity,
                        ease: "linear"
                    }}
                />
            ))}

            {/* Scanning lines */}
            <motion.div
                className="absolute inset-0 bg-gradient-to-b from-transparent via-white/5 to-transparent h-20"
                animate={{ y: ['-100px', 'calc(100vh + 100px)'] }}
                transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "linear"
                }}
            />
        </div>
    );

    // Progress simulation
    useEffect(() => {
        const totalDuration = phases.reduce((sum, phase) => sum + phase.duration, 0);
        let elapsed = 0;

        const interval = setInterval(() => {
            elapsed += 50;
            const newProgress = Math.min((elapsed / totalDuration) * 100, 100);
            setProgress(newProgress);

            // Update current phase
            let phaseElapsed = 0;
            for (let i = 0; i < phases.length; i++) {
                phaseElapsed += phases[i].duration;
                if (elapsed <= phaseElapsed) {
                    setCurrentPhase(i);
                    break;
                }
            }

            if (newProgress >= 100) {
                clearInterval(interval);
                setTimeout(() => setShowClickPrompt(true), 500);
            }
        }, 50);

        return () => clearInterval(interval);
    }, [phases]);

    const handleClick = async () => {
        if (!showClickPrompt || audioUnlocked) return;

        try {
            const audio = new Audio();
            await audio.play();
            audio.pause();
            setAudioUnlocked(true);

            setIsComplete(true);
            setTimeout(() => onComplete(), 800);
        } catch {
            setIsComplete(true);
            setTimeout(() => onComplete(), 800);
        }
    };

    return (
        <AnimatePresence>
            {!isComplete && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0, scale: 1.1 }}
                    transition={{ duration: 0.8 }}
                    className="fixed inset-0 z-50 bg-gradient-to-br from-slate-900 via-gray-900 to-black flex items-center justify-center cursor-pointer"
                    onClick={handleClick}
                >
                    <GeometricBackground />

                    {/* Main content */}
                    <div className="relative z-10 text-center max-w-4xl mx-auto px-8">
                        {/* Logo section */}
                        <motion.div
                            initial={{ y: -50, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ duration: 1, ease: "easeOut" }}
                            className="mb-16"
                        >
                            <div className="relative">
                                <motion.div
                                    className="text-8xl font-light text-white mb-6 tracking-wider"
                                    animate={{
                                        textShadow: [
                                            "0 0 20px rgba(255,255,255,0.5)",
                                            "0 0 40px rgba(255,255,255,0.8)",
                                            "0 0 20px rgba(255,255,255,0.5)"
                                        ]
                                    }}
                                    transition={{ duration: 2, repeat: Infinity }}
                                >
                                    MTD
                                </motion.div>

                                <motion.div
                                    initial={{ width: 0 }}
                                    animate={{ width: "100%" }}
                                    transition={{ duration: 1.5, delay: 0.5 }}
                                    className="h-px bg-gradient-to-r from-transparent via-white to-transparent mb-4"
                                />

                                <div className="text-xl text-white/80 font-light tracking-widest">
                                    MIKTAD TAHIR DURAK
                                </div>
                                <div className="text-sm text-white/60 font-light tracking-wider mt-2">
                                    DIGITAL ENTREPRENEUR & VENTURE ARCHITECT
                                </div>
                            </div>
                        </motion.div>

                        {/* Progress section */}
                        <motion.div
                            initial={{ y: 50, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ duration: 1, delay: 0.3 }}
                            className="mb-12"
                        >
                            {/* Current phase indicator */}
                            <div className="mb-8">
                                <motion.div
                                    key={currentPhase}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="text-white/90 font-mono text-lg mb-2"
                                >
                                    {phases[currentPhase]?.name.replace(/_/g, ' ')}
                                </motion.div>

                                <div className="flex justify-center space-x-2">
                                    {phases.map((_, index) => (
                                        <motion.div
                                            key={index}
                                            className={`w-2 h-2 rounded-full ${
                                                index <= currentPhase ? 'bg-white' : 'bg-white/30'
                                            }`}
                                            animate={index === currentPhase ? {
                                                scale: [1, 1.5, 1],
                                                opacity: [1, 0.5, 1]
                                            } : {}}
                                            transition={{ duration: 1, repeat: Infinity }}
                                        />
                                    ))}
                                </div>
                            </div>

                            {/* Progress bar */}
                            <div className="relative w-full max-w-2xl mx-auto">
                                <div className="h-1 bg-white/20 rounded-full overflow-hidden">
                                    <motion.div
                                        className="h-full bg-gradient-to-r from-white via-white/80 to-white rounded-full"
                                        style={{ width: `${progress}%` }}
                                        transition={{ duration: 0.3, ease: "easeOut" }}
                                    />
                                </div>

                                <motion.div
                                    className="text-white/70 font-mono text-sm mt-4"
                                    animate={{ opacity: [0.7, 1, 0.7] }}
                                    transition={{ duration: 1.5, repeat: Infinity }}
                                >
                                    {Math.round(progress)}% COMPLETE
                                </motion.div>
                            </div>
                        </motion.div>

                        {/* Click prompt */}
                        <AnimatePresence>
                            {showClickPrompt && (
                                <motion.div
                                    initial={{ opacity: 0, scale: 0.9 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    exit={{ opacity: 0, scale: 1.1 }}
                                    className="text-center"
                                >
                                    <motion.div
                                        animate={{
                                            opacity: [0.6, 1, 0.6],
                                            scale: [1, 1.05, 1]
                                        }}
                                        transition={{ duration: 2, repeat: Infinity }}
                                        className="text-white text-xl font-light mb-4"
                                    >
                                        CLICK TO ENTER
                                    </motion.div>

                                    <div className="text-white/50 text-sm font-light">
                                        Initialize audio systems and continue
                                    </div>

                                    {/* Animated border */}
                                    <motion.div
                                        className="mt-6 w-32 h-12 mx-auto border border-white/30 rounded-lg flex items-center justify-center relative overflow-hidden"
                                        whileHover={{ borderColor: "rgba(255,255,255,0.6)" }}
                                    >
                                        <motion.div
                                            className="absolute inset-0 bg-white/10"
                                            animate={{ x: ['-100%', '100%'] }}
                                            transition={{ duration: 2, repeat: Infinity }}
                                        />
                                        <span className="relative z-10 text-white/80 font-mono text-sm">
                                            ENTER
                                        </span>
                                    </motion.div>
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};
