'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { TypewriterText } from './TypewriterText';

interface MatrixLoaderProps {
    onComplete: () => void;
}

export const MatrixLoader: React.FC<MatrixLoaderProps> = ({ onComplete }) => {
    const [currentStep, setCurrentStep] = useState(0);
    const [isComplete, setIsComplete] = useState(false);

    const loadingSteps = [
        "Initializing Matrix Protocol...",
        "Establishing Secure Connection...",
        "Loading Enterprise Database...",
        "Activating Audio Systems...",
        "Welcome to the Matrix."
    ];

    useEffect(() => {
        const timer = setTimeout(() => {
            if (currentStep < loadingSteps.length - 1) {
                setCurrentStep(prev => prev + 1);
            } else {
                setIsComplete(true);
                setTimeout(() => {
                    onComplete();
                }, 1000);
            }
        }, 1200);

        return () => clearTimeout(timer);
    }, [currentStep, loadingSteps.length, onComplete]);

    // Matrix rain effect
    const MatrixRain = () => {
        const columns = Array.from({ length: 50 }, (_, i) => i);
        
        return (
            <div className="absolute inset-0 overflow-hidden opacity-20">
                {columns.map((col) => (
                    <motion.div
                        key={col}
                        className="absolute top-0 text-[#00ff00] font-mono text-sm"
                        style={{ left: `${col * 2}%` }}
                        initial={{ y: -100 }}
                        animate={{ y: '100vh' }}
                        transition={{
                            duration: Math.random() * 3 + 2,
                            repeat: Infinity,
                            delay: Math.random() * 2,
                            ease: 'linear'
                        }}
                    >
                        {Array.from({ length: 20 }, () => 
                            String.fromCharCode(0x30A0 + Math.random() * 96)
                        ).join('')}
                    </motion.div>
                ))}
            </div>
        );
    };

    return (
        <AnimatePresence>
            {!isComplete && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.5 }}
                    className="fixed inset-0 z-50 bg-black flex items-center justify-center"
                >
                    <MatrixRain />
                    
                    <div className="relative z-10 text-center max-w-2xl mx-auto px-8">
                        {/* Matrix Logo */}
                        <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ duration: 0.8, ease: "easeOut" }}
                            className="mb-12"
                        >
                            <div className="text-6xl font-mono text-[#00ff00] mb-4">
                                <motion.span
                                    animate={{ opacity: [1, 0.3, 1] }}
                                    transition={{ duration: 2, repeat: Infinity }}
                                >
                                    [M]
                                </motion.span>
                                <motion.span
                                    animate={{ opacity: [1, 0.3, 1] }}
                                    transition={{ duration: 2, repeat: Infinity, delay: 0.2 }}
                                >
                                    [T]
                                </motion.span>
                                <motion.span
                                    animate={{ opacity: [1, 0.3, 1] }}
                                    transition={{ duration: 2, repeat: Infinity, delay: 0.4 }}
                                >
                                    [D]
                                </motion.span>
                            </div>
                            <div className="text-xl text-white/80 font-mono">
                                MIKTAD TAHIR DURAK
                            </div>
                            <div className="text-sm text-[#00ff00]/60 font-mono mt-2">
                                DIGITAL ENTREPRENEUR
                            </div>
                        </motion.div>

                        {/* Loading Steps */}
                        <div className="space-y-6">
                            {loadingSteps.map((step, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ 
                                        opacity: index <= currentStep ? 1 : 0.3,
                                        x: index <= currentStep ? 0 : -20
                                    }}
                                    transition={{ duration: 0.5 }}
                                    className="flex items-center justify-center"
                                >
                                    <div className="flex items-center space-x-4">
                                        <motion.div
                                            animate={index === currentStep ? { 
                                                rotate: 360,
                                                scale: [1, 1.2, 1]
                                            } : {}}
                                            transition={{ 
                                                rotate: { duration: 1, repeat: Infinity, ease: "linear" },
                                                scale: { duration: 0.5, repeat: Infinity }
                                            }}
                                            className="w-4 h-4 border-2 border-[#00ff00] border-t-transparent rounded-full"
                                        />
                                        <div className="text-white font-mono text-lg">
                                            {index === currentStep ? (
                                                <TypewriterText
                                                    text={step}
                                                    className="text-[#00ff00]"
                                                />
                                            ) : index < currentStep ? (
                                                <span className="text-[#00ff00]">{step}</span>
                                            ) : (
                                                <span className="text-white/40">{step}</span>
                                            )}
                                        </div>
                                        {index < currentStep && (
                                            <motion.div
                                                initial={{ scale: 0 }}
                                                animate={{ scale: 1 }}
                                                className="text-[#00ff00] text-xl"
                                            >
                                                ✓
                                            </motion.div>
                                        )}
                                    </div>
                                </motion.div>
                            ))}
                        </div>

                        {/* Progress Bar */}
                        <motion.div
                            className="mt-12 w-full max-w-md mx-auto"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.5 }}
                        >
                            <div className="w-full bg-white/10 rounded-full h-2 overflow-hidden">
                                <motion.div
                                    className="h-full bg-gradient-to-r from-[#00ff00] to-[#00cc00]"
                                    initial={{ width: 0 }}
                                    animate={{ width: `${((currentStep + 1) / loadingSteps.length) * 100}%` }}
                                    transition={{ duration: 0.8, ease: "easeOut" }}
                                />
                            </div>
                            <div className="text-center mt-2 text-[#00ff00] font-mono text-sm">
                                {Math.round(((currentStep + 1) / loadingSteps.length) * 100)}%
                            </div>
                        </motion.div>
                    </div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};
