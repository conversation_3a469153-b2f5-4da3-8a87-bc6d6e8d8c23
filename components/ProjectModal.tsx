'use client';

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { motion } from 'framer-motion';
import { X, Upload, Loader } from 'lucide-react';
import { useProjects } from '@/hooks/useProjects';
import { MatrixText } from './matrix/MatrixText';
import { TerminalWindow } from './matrix/TerminalWindow';
import Image from 'next/image';

interface Project {
    id: string;
    title: string;
    description: string;
    tech: string[];
    link?: string;
    image: string;
}

interface ProjectModalProps {
    project: Project | null;
    onClose: () => void;
}

interface FormData {
    title: string;
    description: string;
    tech: string;
    link: string;
    image: string;
}

const ProjectModal: React.FC<ProjectModalProps> = ({ project, onClose }) => {
    const { addProject, updateProject } = useProjects();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [formData, setFormData] = useState<FormData>({
        title: project?.title || '',
        description: project?.description || '',
        tech: project?.tech?.join(', ') || '',
        link: project?.link || '',
        image: project?.image || ''
    });
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState(project?.image || '');

    useEffect(() => {
        setFormData({
            title: project?.title || '',
            description: project?.description || '',
            tech: project?.tech?.join(', ') || '',
            link: project?.link || '',
            image: project?.image || ''
        });
        setImagePreview(project?.image || '');
        setImageFile(null);
        setError(null);
    }, [project]);

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setImageFile(file);
            const reader = new FileReader();
            reader.onloadend = () => {
                setImagePreview(reader.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        setError(null);

        try {
            const techArray: string[] = formData.tech.split(',').map((t: string) => t.trim());
            const projectData = {
                ...formData,
                tech: techArray,
            };

            if (project?.id) {
                await updateProject(project.id, projectData, imageFile || undefined);
            } else {
                if (!imageFile) throw new Error('Image is required');
                await addProject(projectData, imageFile);
            }
            onClose();
        } catch (error) {
            console.error('Error saving project:', error);
            setError('Failed to save project. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
            <Head>
                <title>{project ? 'Update Project' : 'New Project'}</title>
                <meta name="description" content={project ? project.description : 'Create a new project'} />
                <script type="application/ld+json">
                    {JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "SoftwareApplication",
                        "name": formData.title,
                        "description": formData.description,
                        "applicationCategory": "WebApplication",
                        "operatingSystem": "All",
                        "softwareVersion": "1.0",
                        "offers": {
                            "@type": "Offer",
                            "price": "0.00",
                            "priceCurrency": "USD"
                        }
                    })}
                </script>
            </Head>
            <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="w-full max-w-2xl"
            >
                <TerminalWindow
                    title={`${project ? 'Update' : 'New'}_Enterprise.dmg`}
                    className="backdrop-blur-lg bg-white/10 rounded-3xl shadow-xl"
                >
                    <form onSubmit={handleSubmit} className="p-6 space-y-6">
                        <div className="flex justify-between items-center">
                            <h2 className="text-2xl font-bold text-white">
                                <MatrixText
                                    text={project ? 'Update Enterprise Protocol' : 'Initialize New Venture'}
                                />
                            </h2>
                            <button
                                type="button"
                                onClick={onClose}
                                className="p-2 rounded-full hover:bg-white/10 transition-colors"
                            >
                                <X className="w-5 h-5 text-white/60" />
                            </button>
                        </div>

                        {/* Image Upload */}
                        <div className="relative">
                            <div
                                className={`
                                    w-full h-48 rounded-xl overflow-hidden
                                    ${!imagePreview ? 'border-2 border-dashed border-white/20' : ''}
                                `}
                            >
                                {imagePreview ? (
                                    <Image
                                        src={imagePreview}
                                        alt="Preview"
                                        layout="fill"
                                        objectFit="cover"
                                    />
                                ) : (
                                    <div className="flex flex-col items-center justify-center h-full text-white/60">
                                        <Upload className="w-8 h-8 mb-2" />
                                        <span>Upload Project Image</span>
                                    </div>
                                )}
                            </div>
                            <input
                                type="file"
                                onChange={handleImageChange}
                                accept="image/*"
                                className="absolute inset-0 opacity-0 cursor-pointer"
                            />
                        </div>

                        {/* Form Fields */}
                        <div className="space-y-4">
                            <div>
                                <label className="text-white/60 text-sm font-mono">[ENTERPRISE_NAME]</label>
                                <input
                                    type="text"
                                    value={formData.title}
                                    onChange={e => setFormData(prev => ({ ...prev, title: e.target.value }))}
                                    className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white mt-1"
                                    placeholder="Initialize enterprise designation..."
                                    itemProp="name"
                                />
                            </div>

                            <div>
                                <label className="text-white/60 text-sm font-mono">[VENTURE_DESCRIPTION]</label>
                                <textarea
                                    value={formData.description}
                                    onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
                                    className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white mt-1 h-32"
                                    placeholder="Define enterprise mission and objectives..."
                                    itemProp="description"
                                />
                            </div>

                            <div>
                                <label className="text-white/60 text-sm font-mono">[TECH_STACK]</label>
                                <input
                                    type="text"
                                    value={formData.tech}
                                    onChange={e => setFormData(prev => ({ ...prev, tech: e.target.value }))}
                                    className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white mt-1"
                                    placeholder="React, Node.js, MongoDB (comma separated protocols)"
                                    itemProp="technologies"
                                />
                            </div>

                            <div>
                                <label className="text-white/60 text-sm font-mono">[ACCESS_PORTAL]</label>
                                <input
                                    type="url"
                                    value={formData.link}
                                    onChange={e => setFormData(prev => ({ ...prev, link: e.target.value }))}
                                    className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white mt-1"
                                    placeholder="https://enterprise-portal.com"
                                    itemProp="url"
                                />
                            </div>
                        </div>

                        {/* Error Message */}
                        {error && (
                            <div className="text-red-500 text-sm">
                                {error}
                            </div>
                        )}

                        {/* Submit Button */}
                        <div className="flex justify-end pt-4">
                            <button
                                type="submit"
                                disabled={loading}
                                className="inline-flex items-center px-6 py-2 rounded-full
                                    bg-white/10 hover:bg-white/20 text-white transition-colors"
                            >
                                {loading ? (
                                    <>
                                        <Loader className="w-4 h-4 mr-2 animate-spin" />
                                        Processing...
                                    </>
                                ) : (
                                    <MatrixText text={project ? 'Update Enterprise' : 'Deploy Venture'} />
                                )}
                            </button>
                        </div>
                    </form>
                </TerminalWindow>
            </motion.div>
        </div>
    );
};

export default ProjectModal;