'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

interface MasonryGridProps {
    children: React.ReactElement[];
    columns?: {
        default: number;
        1024: number;
        768: number;
        640: number;
    };
    gap?: number;
    className?: string;
}

export const MasonryGrid: React.FC<MasonryGridProps> = ({
    children,
    columns = {
        default: 3,
        1024: 3,
        768: 2,
        640: 1
    },
    gap = 24,
    className = ''
}) => {
    const [columnCount, setColumnCount] = useState(columns.default);
    const [columnHeights, setColumnHeights] = useState<number[]>([]);
    const containerRef = useRef<HTMLDivElement>(null);

    // Responsive column count
    useEffect(() => {
        const updateColumns = () => {
            const width = window.innerWidth;
            if (width < 640) {
                setColumnCount(columns[640]);
            } else if (width < 768) {
                setColumnCount(columns[768]);
            } else if (width < 1024) {
                setColumnCount(columns[1024]);
            } else {
                setColumnCount(columns.default);
            }
        };

        updateColumns();
        window.addEventListener('resize', updateColumns);
        return () => window.removeEventListener('resize', updateColumns);
    }, [columns]);

    // Initialize column heights
    useEffect(() => {
        setColumnHeights(new Array(columnCount).fill(0));
    }, [columnCount]);

    // Get the shortest column index
    const getShortestColumnIndex = () => {
        return columnHeights.indexOf(Math.min(...columnHeights));
    };

    // Distribute children into columns
    const distributeChildren = () => {
        const columnsArray: React.ReactElement[][] = Array.from({ length: columnCount }, () => []);
        const tempHeights = new Array(columnCount).fill(0);

        children.forEach((child, index) => {
            const shortestColumnIndex = tempHeights.indexOf(Math.min(...tempHeights));
            
            // Clone child with masonry-specific props
            const clonedChild = React.cloneElement(child, {
                key: child.key || index,
                style: {
                    ...child.props.style,
                    marginBottom: `${gap}px`,
                    breakInside: 'avoid'
                }
            });

            columnsArray[shortestColumnIndex].push(clonedChild);
            
            // Estimate height (this is approximate, real height will be calculated after render)
            tempHeights[shortestColumnIndex] += 300; // Approximate height
        });

        return columnsArray;
    };

    const columns_array = distributeChildren();

    return (
        <div 
            ref={containerRef}
            className={`masonry-grid ${className}`}
            style={{
                display: 'flex',
                gap: `${gap}px`,
                alignItems: 'flex-start'
            }}
        >
            {columns_array.map((column, columnIndex) => (
                <motion.div
                    key={columnIndex}
                    className="masonry-column"
                    style={{
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column'
                    }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: columnIndex * 0.1 }}
                >
                    {column}
                </motion.div>
            ))}
        </div>
    );
};

// CSS-only masonry alternative (more performant but less control)
export const CSSMasonryGrid: React.FC<{
    children: React.ReactNode;
    columns?: number;
    gap?: number;
    className?: string;
}> = ({
    children,
    columns = 3,
    gap = 24,
    className = ''
}) => {
    return (
        <div 
            className={`css-masonry-grid ${className}`}
            style={{
                columnCount: columns,
                columnGap: `${gap}px`,
                columnFill: 'balance'
            }}
        >
            {React.Children.map(children, (child, index) => (
                <div 
                    key={index}
                    style={{
                        breakInside: 'avoid',
                        marginBottom: `${gap}px`,
                        display: 'inline-block',
                        width: '100%'
                    }}
                >
                    {child}
                </div>
            ))}
        </div>
    );
};
