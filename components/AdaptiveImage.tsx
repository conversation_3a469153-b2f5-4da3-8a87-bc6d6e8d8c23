'use client';

import React, { useState } from 'react';
import Image from 'next/image';

interface AdaptiveImageProps {
    src: string;
    alt: string;
    className?: string;
    onClick?: () => void;
    showHoverEffect?: boolean;
    useMasonryLayout?: boolean;
    maxHeight?: number;
}

export const AdaptiveImage: React.FC<AdaptiveImageProps> = ({
    src,
    alt,
    className = '',
    onClick,
    showHoverEffect = false,
    useMasonryLayout = false,
    maxHeight = 600
}) => {
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });
    const [imageError, setImageError] = useState(false);

    const handleImageLoad = (event: React.SyntheticEvent<HTMLImageElement>) => {
        const img = event.currentTarget;
        setImageDimensions({ width: img.naturalWidth, height: img.naturalHeight });
        setImageLoaded(true);
        setImageError(false);
    };

    const handleImageError = () => {
        setImageError(true);
        setImageLoaded(false);
    };

    // Calculate aspect ratio and determine image type
    const aspectRatio = imageDimensions.width / imageDimensions.height;
    const isPortrait = imageDimensions.height > imageDimensions.width;
    const isSquare = Math.abs(aspectRatio - 1) < 0.1;
    const isWide = aspectRatio > 2;

    // Determine container class based on image orientation
    const getContainerClass = () => {
        if (useMasonryLayout) {
            // For masonry layout, use natural aspect ratio
            return '';
        }

        if (!imageLoaded) return 'h-64'; // Default while loading

        if (isPortrait) {
            return 'memory-image-portrait';
        } else if (isSquare) {
            return 'memory-image-square';
        } else if (isWide) {
            return 'memory-image-wide';
        } else {
            return 'memory-image-landscape';
        }
    };

    // Calculate dynamic height for masonry layout
    const getMasonryHeight = () => {
        if (!useMasonryLayout || !imageLoaded) return undefined;

        const containerWidth = 300; // Approximate container width
        const calculatedHeight = (containerWidth / aspectRatio);
        return Math.min(calculatedHeight, maxHeight);
    };

    const masonryHeight = getMasonryHeight();

    return (
        <div
            className={`memory-image-container ${getContainerClass()} ${className} ${
                onClick ? 'cursor-pointer' : ''
            } ${showHoverEffect ? 'group/adaptive-image' : ''}`}
            onClick={onClick}
            style={useMasonryLayout && masonryHeight ? { height: masonryHeight } : undefined}
        >
            {!imageError ? (
                <>
                    {useMasonryLayout ? (
                        <Image
                            src={src}
                            alt={alt}
                            width={0}
                            height={0}
                            className={`memory-image w-full h-auto ${
                                showHoverEffect ? 'group-hover/adaptive-image:scale-105' : ''
                            } transition-transform duration-300`}
                            onLoad={handleImageLoad}
                            onError={handleImageError}
                            style={{
                                width: '100%',
                                height: 'auto',
                                maxHeight: maxHeight
                            }}
                            sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                        />
                    ) : (
                        <Image
                            src={src}
                            alt={alt}
                            fill
                            className={`memory-image object-cover ${
                                showHoverEffect ? 'group-hover/adaptive-image:scale-105' : ''
                            } transition-transform duration-300`}
                            onLoad={handleImageLoad}
                            onError={handleImageError}
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                    )}
                    
                    {/* Loading state */}
                    {!imageLoaded && (
                        <div className="absolute inset-0 flex items-center justify-center bg-white/5 image-loading">
                            <div className="text-white/60 text-sm">Loading...</div>
                        </div>
                    )}
                    
                    {/* Hover overlay */}
                    {showHoverEffect && onClick && (
                        <div className="absolute inset-0 bg-black/0 group-hover/adaptive-image:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                            <div className="text-white opacity-0 group-hover/adaptive-image:opacity-100 transition-opacity duration-300 text-sm font-medium">
                                Click to view full size
                            </div>
                        </div>
                    )}
                </>
            ) : (
                <div className="absolute inset-0 flex items-center justify-center bg-white/5 rounded-lg">
                    <div className="text-white/60 text-sm text-center">
                        <div className="mb-2">Failed to load image</div>
                        <div className="text-xs text-white/40">Check image URL</div>
                    </div>
                </div>
            )}
        </div>
    );
};
