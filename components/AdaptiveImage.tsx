'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';

interface AdaptiveImageProps {
    src: string;
    alt: string;
    className?: string;
    onClick?: () => void;
    showHoverEffect?: boolean;
}

export const AdaptiveImage: React.FC<AdaptiveImageProps> = ({
    src,
    alt,
    className = '',
    onClick,
    showHoverEffect = false
}) => {
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });
    const [imageError, setImageError] = useState(false);

    const handleImageLoad = (event: React.SyntheticEvent<HTMLImageElement>) => {
        const img = event.currentTarget;
        setImageDimensions({ width: img.naturalWidth, height: img.naturalHeight });
        setImageLoaded(true);
        setImageError(false);
    };

    const handleImageError = () => {
        setImageError(true);
        setImageLoaded(false);
    };

    // Calculate aspect ratio and determine image type
    const aspectRatio = imageDimensions.width / imageDimensions.height;
    const isPortrait = imageDimensions.height > imageDimensions.width;
    const isSquare = Math.abs(aspectRatio - 1) < 0.1;
    const isWide = aspectRatio > 2;

    // Determine container class based on image orientation
    const getContainerClass = () => {
        if (!imageLoaded) return 'h-64'; // Default while loading
        
        if (isPortrait) {
            return 'memory-image-portrait';
        } else if (isSquare) {
            return 'memory-image-square';
        } else if (isWide) {
            return 'memory-image-wide';
        } else {
            return 'memory-image-landscape';
        }
    };

    return (
        <div 
            className={`memory-image-container ${getContainerClass()} ${className} ${
                onClick ? 'cursor-pointer' : ''
            } ${showHoverEffect ? 'group/adaptive-image' : ''}`}
            onClick={onClick}
        >
            {!imageError ? (
                <>
                    <Image
                        src={src}
                        alt={alt}
                        fill
                        className={`memory-image object-cover ${
                            showHoverEffect ? 'group-hover/adaptive-image:scale-105' : ''
                        } transition-transform duration-300`}
                        onLoad={handleImageLoad}
                        onError={handleImageError}
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                    
                    {/* Loading state */}
                    {!imageLoaded && (
                        <div className="absolute inset-0 flex items-center justify-center bg-white/5 image-loading">
                            <div className="text-white/60 text-sm">Loading...</div>
                        </div>
                    )}
                    
                    {/* Hover overlay */}
                    {showHoverEffect && onClick && (
                        <div className="absolute inset-0 bg-black/0 group-hover/adaptive-image:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                            <div className="text-white opacity-0 group-hover/adaptive-image:opacity-100 transition-opacity duration-300 text-sm font-medium">
                                Click to view full size
                            </div>
                        </div>
                    )}
                </>
            ) : (
                <div className="absolute inset-0 flex items-center justify-center bg-white/5 rounded-lg">
                    <div className="text-white/60 text-sm text-center">
                        <div className="mb-2">Failed to load image</div>
                        <div className="text-xs text-white/40">Check image URL</div>
                    </div>
                </div>
            )}
        </div>
    );
};
